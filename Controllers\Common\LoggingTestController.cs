using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Ims;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Controllers.Common
{
    /// <summary>
    /// 日誌系統測試控制器
    /// 用於驗證新的 MongoDB 日誌系統功能
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class LoggingTestController : ControllerBase
    {
        private readonly ERPDbContext _context;
        private readonly IResilientLoggerService _logger;

        public LoggingTestController(ERPDbContext context, IResilientLoggerService logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 測試基本日誌記錄功能
        /// </summary>
        [HttpPost("test-basic-logging")]
        public async Task<IActionResult> TestBasicLogging()
        {
            try
            {
                await _logger.LogInfoAsync("測試基本日誌記錄功能", "LoggingTestController");
                await _logger.LogWarningAsync("測試警告日誌", "LoggingTestController");
                await _logger.LogErrorAsync("測試錯誤日誌", new Exception("測試例外"), "LoggingTestController");

                return Ok(new { success = true, message = "基本日誌記錄測試完成" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 測試實體變更日誌記錄（簡單實體）
        /// </summary>
        [HttpPost("test-simple-entity-change")]
        public async Task<IActionResult> TestSimpleEntityChange()
        {
            try
            {
                // 創建一個測試 Partner
                var partner = new Partner
                {
                    PartnerID = Guid.NewGuid().ToString(),
                    Name = "測試夥伴",
                    Type = "Customer",
                    Status = "Active",
                    CreateTime = DateTime.UtcNow.AddHours(8),
                    CreateUserId = "test-user",
                    UpdateTime = DateTime.UtcNow.AddHours(8),
                    UpdateUserId = "test-user"
                };

                _context.Partners.Add(partner);
                await _context.SaveChangesAsync();

                return Ok(new { 
                    success = true, 
                    message = "簡單實體變更測試完成",
                    partnerId = partner.PartnerID
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 測試複雜實體變更日誌記錄（包含導航屬性）
        /// </summary>
        [HttpPost("test-complex-entity-change")]
        public async Task<IActionResult> TestComplexEntityChange()
        {
            try
            {
                // 創建一個包含詳細資料的 Partner
                var partner = new Partner
                {
                    PartnerID = Guid.NewGuid().ToString(),
                    Name = "測試複雜夥伴",
                    Type = "Customer",
                    Status = "Active",
                    CreateTime = DateTime.UtcNow.AddHours(8),
                    CreateUserId = "test-user",
                    UpdateTime = DateTime.UtcNow.AddHours(8),
                    UpdateUserId = "test-user"
                };

                // 添加客戶詳細資料
                var customerDetail = new CustomerDetail
                {
                    CustomerDetailID = Guid.NewGuid().ToString(),
                    PartnerID = partner.PartnerID,
                    CreditLimit = 100000,
                    PaymentTerms = "Net 30",
                    CreateTime = DateTime.UtcNow.AddHours(8),
                    CreateUserId = "test-user",
                    UpdateTime = DateTime.UtcNow.AddHours(8),
                    UpdateUserId = "test-user"
                };

                _context.Partners.Add(partner);
                _context.CustomerDetails.Add(customerDetail);
                await _context.SaveChangesAsync();

                // 修改 Partner 以測試更新日誌
                partner.Name = "修改後的複雜夥伴";
                partner.UpdateTime = DateTime.UtcNow.AddHours(8);
                await _context.SaveChangesAsync();

                return Ok(new { 
                    success = true, 
                    message = "複雜實體變更測試完成",
                    partnerId = partner.PartnerID,
                    customerDetailId = customerDetail.CustomerDetailID
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 測試日誌系統健康狀態
        /// </summary>
        [HttpGet("health-status")]
        public async Task<IActionResult> GetHealthStatus()
        {
            try
            {
                var healthStatus = await _logger.GetHealthStatusAsync();
                return Ok(healthStatus);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 重置熔斷器
        /// </summary>
        [HttpPost("reset-circuit-breaker")]
        public async Task<IActionResult> ResetCircuitBreaker()
        {
            try
            {
                await _logger.ResetCircuitBreakerAsync();
                return Ok(new { success = true, message = "熔斷器已重置" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 測試大量日誌記錄（效能測試）
        /// </summary>
        [HttpPost("test-performance")]
        public async Task<IActionResult> TestPerformance([FromQuery] int logCount = 100)
        {
            try
            {
                var startTime = DateTime.UtcNow;
                var tasks = new List<Task>();

                for (int i = 0; i < logCount; i++)
                {
                    tasks.Add(_logger.LogInfoAsync($"效能測試日誌 #{i}", "LoggingTestController"));
                }

                await Task.WhenAll(tasks);
                var endTime = DateTime.UtcNow;
                var duration = endTime - startTime;

                return Ok(new { 
                    success = true, 
                    message = $"效能測試完成，記錄 {logCount} 條日誌",
                    duration = duration.TotalMilliseconds,
                    averagePerLog = duration.TotalMilliseconds / logCount
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }
    }
}
