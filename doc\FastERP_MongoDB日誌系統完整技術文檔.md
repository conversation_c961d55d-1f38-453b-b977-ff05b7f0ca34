# FastERP MongoDB 日誌系統完整技術文檔

## 📋 **系統概述**

### **設計目標**
新版 MongoDB 日誌系統旨在解決現有系統的根本性問題，提供穩定、可靠、高效能的企業級日誌記錄解決方案。

### **核心改進**
- ✅ **徹底解決循環引用問題**: 智能序列化機制，完全避免 Entity Framework 導航屬性循環引用
- ✅ **強化連接管理**: 專用連接管理器，提供連接池、健康檢查和自動重連機制
- ✅ **簡化資料結構**: 清晰易讀的日誌格式，避免複雜的 _t/_v 嵌套結構
- ✅ **彈性錯誤處理**: 熔斷器模式、重試機制和降級處理，確保業務操作不受影響
- ✅ **完整審計追蹤**: 詳細的 before/after 資料記錄，支援複雜實體層次結構

---

## 🏗️ **系統架構**

### **核心組件**

#### **1. IMongoDBConnectionManager - 連接管理器**
```csharp
/// <summary>
/// MongoDB 連接管理器
/// 提供連接池管理、健康檢查和重連機制
/// </summary>
public interface IMongoDBConnectionManager
{
    Task<IMongoCollection<T>> GetCollectionAsync<T>(string collectionName);
    Task<bool> HealthCheckAsync();
    Task<bool> ResetConnectionAsync();
    Task<MongoDBConnectionStatus> GetConnectionStatusAsync();
}
```

**主要功能:**
- 連接池管理和重用
- 自動健康檢查
- 連接失敗自動重連
- 連接狀態監控

#### **2. ILogDataProcessor - 資料處理器**
```csharp
/// <summary>
/// 日誌資料處理器
/// 負責安全序列化和資料格式化
/// </summary>
public interface ILogDataProcessor
{
    Task<SimpleLogEntry> ProcessEntityChangeAsync<T>(EntityChangeRecord<T> changeRecord) where T : class;
    Task<SimpleLogEntry> ProcessLogEntryAsync(string message, object data, string transactionId, string source);
    string SafeSerialize(object obj);
}
```

**主要功能:**
- 安全序列化，避免循環引用
- 敏感資料過濾
- 批次變更處理
- 統一資料格式化

#### **3. IResilientLoggerService - 彈性日誌服務**
```csharp
/// <summary>
/// 具有彈性和錯誤處理能力的日誌服務
/// 提供重試機制、熔斷器模式和降級處理
/// </summary>
public interface IResilientLoggerService : ILoggerService
{
    Task<bool> TryLogAsync(SimpleLogEntry entry, int maxRetries = 3);
    Task<LoggingHealthStatus> GetHealthStatusAsync();
    void EnableCircuitBreaker(bool enabled);
    Task ResetCircuitBreakerAsync();
}
```

**主要功能:**
- 智能重試機制
- 熔斷器保護
- 降級處理
- 健康狀態監控

### **資料流程**
```
實體變更 → EnhancedEntityChangeTracker → EntityChangeRecord
    ↓
LogDataProcessor → 安全序列化 → SimpleLogEntry
    ↓
ResilientLoggerService → 重試/熔斷器 → MongoDB
    ↓
連接管理器 → 健康檢查 → 成功寫入
```

---

## 📊 **資料模型**

### **SimpleLogEntry - 簡化日誌條目**
```csharp
public class SimpleLogEntry
{
    public ObjectId Id { get; set; }
    public DateTime Timestamp { get; set; }
    public string Level { get; set; }
    public string Message { get; set; }
    public string Source { get; set; }
    public string TransactionId { get; set; }
    public LogData Data { get; set; }
    public string UserId { get; set; }
    public string IpAddress { get; set; }
    public string RequestUrl { get; set; }
    public string UserAgent { get; set; }
    public string ErrorMessage { get; set; }
    public string StackTrace { get; set; }
}
```

### **LogData - 日誌資料結構**
```csharp
public class LogData
{
    public string Operation { get; set; }
    public string EntityType { get; set; }
    public string EntityId { get; set; }
    public string Summary { get; set; }
    public string BeforeData { get; set; }
    public string AfterData { get; set; }
    public List<string> ChangedFields { get; set; }
    public string Status { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; }
}
```

---

## ⚙️ **配置設定**

### **appsettings.json 配置**
```json
{
  "MongoDB": {
    "ConnectionString": "mongodb://sa:<EMAIL>:27017/?authMechanism=SCRAM-SHA-256",
    "DatabaseName": "FAST_ERP",
    "CollectionName": "Logger_New"
  },
  "Logging": {
    "Resilient": {
      "RetryOptions": {
        "MaxRetries": 3,
        "BaseDelayMs": 1000,
        "MaxDelayMs": 30000,
        "UseExponentialBackoff": true,
        "UseJitter": true
      },
      "CircuitBreaker": {
        "FailureThreshold": 5,
        "SuccessThreshold": 3,
        "TimeoutMs": 60000,
        "MonitoringWindowMs": 300000,
        "Enabled": true
      }
    }
  }
}
```

### **Program.cs 服務註冊**
```csharp
// MongoDB 日誌系統服務註冊
builder.Services.AddSingleton<IMongoDBConnectionManager, MongoDBConnectionManager>();
builder.Services.AddSingleton<ILogDataProcessor, LogDataProcessor>();
builder.Services.AddSingleton<LogProcessingOptions>();
builder.Services.AddSingleton<IResilientLoggerService, ResilientMongoDBLoggerService>();
builder.Services.AddSingleton<ILoggerService, ResilientMongoDBLoggerService>();
```

---

## 🚀 **實施指南**

### **步驟 1: 環境準備**

#### **1.1 MongoDB 配置驗證**
```bash
# 檢查 MongoDB 連接
mongo "mongodb://sa:<EMAIL>:27017/?authMechanism=SCRAM-SHA-256"

# 驗證資料庫和集合
use FAST_ERP
db.Logger_New.find().limit(1)
```

#### **1.2 依賴項檢查**
```bash
# 確保所有必要的 NuGet 套件已安裝
dotnet list package | grep MongoDB
dotnet list package | grep Newtonsoft
```

### **步驟 2: 編譯和部署**

#### **2.1 編譯驗證**
```bash
# 清理並重建專案
dotnet clean
dotnet build

# 檢查編譯錯誤
dotnet build --verbosity normal
```

#### **2.2 單元測試執行**
```bash
# 執行所有測試
dotnet test

# 執行特定的日誌相關測試
dotnet test --filter "Category=Logging"
```

### **步驟 3: 功能驗證**

#### **3.1 基本功能測試**
```bash
# 測試基本功能
curl -X POST "http://localhost:5000/api/LoggingMigration/test/basic-functionality"

# 檢查健康狀態
curl -X GET "http://localhost:5000/api/LoggingMigration/health/new-system"

# 效能測試
curl -X POST "http://localhost:5000/api/LoggingMigration/test/performance?logCount=100"
```

#### **3.2 實體變更測試**
```bash
# 測試簡單實體變更
curl -X POST "https://localhost:7137/api/LoggingFixTest/test-simple-entity-change"

# 測試複雜實體變更
curl -X POST "https://localhost:7137/api/LoggingFixTest/test-complex-entity-change"
```

---

## 🔍 **監控和維護**

### **健康檢查指標**
- **連接狀態**: MongoDB 連接是否正常
- **成功率**: 日誌記錄成功率（目標 > 99.5%）
- **回應時間**: 平均日誌記錄時間（目標 < 100ms）
- **熔斷器狀態**: 熔斷器當前狀態
- **錯誤統計**: 各類錯誤的統計資訊

### **API 端點**
```
GET  /api/LoggingMigration/health/new-system     - 獲取系統健康狀態
GET  /api/LoggingMigration/health/error-statistics - 獲取錯誤統計
POST /api/LoggingMigration/test/basic-functionality - 測試基本功能
POST /api/LoggingMigration/test/performance      - 效能測試
POST /api/LoggingMigration/circuit-breaker/reset - 重置熔斷器
```

### **MongoDB 查詢範例**
```javascript
// 查詢特定實體的變更記錄
db.Logger_New.find({
  "data.entityType": "Partner",
  "data.entityId": "partner-001"
}).sort({ "timestamp": -1 })

// 查詢特定時間範圍的錯誤日誌
db.Logger_New.find({
  "level": "Error",
  "timestamp": {
    "$gte": ISODate("2024-01-01T00:00:00Z"),
    "$lt": ISODate("2024-01-02T00:00:00Z")
  }
})

// 查詢特定交易的所有相關日誌
db.Logger_New.find({
  "transactionId": "87654321-4321-4321-4321-210987654321"
}).sort({ "timestamp": 1 })
```

---

## 🛡️ **安全性和合規**

### **資料保護**
- **敏感資料過濾**: 自動過濾密碼、金鑰等敏感欄位
- **資料脫敏**: 支援自定義敏感欄位清單
- **存取控制**: 基於角色的日誌存取權限
- **資料加密**: 支援 MongoDB 傳輸加密

### **隱私合規**
- **GDPR 合規**: 支援個人資料匿名化
- **資料保留**: 可配置的日誌保留期限
- **審計追蹤**: 完整的操作審計記錄
- **資料完整性**: 防篡改的日誌記錄機制

---

## 🚀 **效能特性**

### **效能指標**
- **單一日誌記錄**: < 50ms
- **批次日誌記錄**: < 200ms (100條)
- **並發處理能力**: > 1000 TPS
- **記憶體使用**: < 100MB (正常負載)
- **連接池效率**: 95% 連接重用率

### **優化機制**
- **連接池管理**: 自動管理 MongoDB 連接，避免頻繁建立/關閉連接
- **批次處理**: 支援批次日誌寫入，提升吞吐量
- **非同步處理**: 完全非同步操作，不阻塞主執行緒
- **智能快取**: 實體元數據快取，減少反射操作
- **資料壓縮**: 大型資料自動壓縮，節省儲存空間

---

## 📈 **故障排除**

### **常見問題**

#### **1. MongoDB 連接失敗**
```
症狀: 日誌記錄失敗，健康檢查顯示連接異常
解決: 檢查 MongoDB 服務狀態，驗證連接字串，重置連接
API: POST /api/LoggingMigration/circuit-breaker/reset
```

#### **2. 熔斷器開啟**
```
症狀: 日誌記錄被阻止，系統進入降級模式
解決: 檢查 MongoDB 狀態，修復問題後重置熔斷器
API: POST /api/LoggingMigration/circuit-breaker/reset
```

#### **3. 效能下降**
```
症狀: 日誌記錄時間過長，系統回應緩慢
解決: 檢查 MongoDB 效能，調整批次大小，優化查詢
監控: GET /api/LoggingMigration/health/error-statistics
```

#### **4. 循環引用序列化錯誤**
```
症狀: afterData 欄位出現 "A possible object cycle was detected" 錯誤
解決: 檢查 LogDataProcessor 配置，確保使用增強的序列化處理
診斷: curl -X POST "https://localhost:7137/api/LoggingFixTest/test-direct-logging"
```

### **診斷工具**
- **健康檢查 API**: 即時系統狀態
- **效能測試 API**: 系統效能驗證
- **錯誤統計 API**: 詳細錯誤分析
- **MongoDB 監控**: 資料庫層面監控

---

## 🔧 **修復驗證指南**

### **已知問題和修復**

#### **循環引用問題**
**問題描述**: Entity Framework 的實體變更追蹤包含了導航屬性，造成循環引用問題。

**錯誤訊息範例**:
```
System.Text.Json.JsonException: A possible object cycle was detected which is not supported.
```

**修復方案**:
1. **增強的序列化處理**
   - 實現了循環引用檢測和處理
   - 添加了多層次的序列化備援機制
   - 使用 `ReferenceHandler.IgnoreCycles` 處理複雜物件

2. **改進的資料提取**
   - 智能過濾導航屬性
   - 只提取安全的基本類型屬性
   - 扁平化複雜資料結構

3. **彈性錯誤處理**
   - 多重備援序列化策略
   - 詳細的錯誤記錄和診斷
   - 確保日誌記錄不會失敗

### **驗證步驟**

#### **步驟 1: 啟動修復後的系統**
```bash
# 重新編譯並啟動系統
dotnet build
dotnet run
```

#### **步驟 2: 測試簡單實體變更**
```bash
# 測試簡單實體創建
curl -X POST "https://localhost:7137/api/LoggingFixTest/test-simple-entity-change"
```

預期結果：
- 成功創建實體
- MongoDB 中記錄完整的 `afterData`
- 沒有序列化錯誤

#### **步驟 3: 測試複雜實體變更**
```bash
# 測試包含導航屬性的複雜實體
curl -X POST "https://localhost:7137/api/LoggingFixTest/test-complex-entity-change"
```

預期結果：
- 成功處理複雜實體
- `beforeData` 和 `afterData` 都正確記錄
- 導航屬性被安全處理，沒有循環引用

#### **步驟 4: 驗證 MongoDB 資料**
```javascript
// 檢查最新的日誌記錄
db.Logger_New.find().sort({timestamp: -1}).limit(5).pretty()

// 驗證資料完整性
db.Logger_New.find({
  "data.afterData": {$exists: true, $ne: null, $ne: ""}
}).count()
```

### **驗證檢查清單**

#### **功能驗證**
- [ ] `beforeData` 包含修改前的完整資料
- [ ] `afterData` 包含修改後的完整資料
- [ ] `changedFields` 正確列出變更的欄位
- [ ] 沒有序列化錯誤訊息
- [ ] `entityType` 和 `entityId` 正確填充

#### **資料完整性驗證**
- [ ] JSON 格式正確且可解析
- [ ] 敏感資料已適當過濾
- [ ] 時間戳準確
- [ ] 使用者資訊正確記錄

#### **效能驗證**
- [ ] 日誌記錄時間 < 100ms
- [ ] 沒有記憶體洩漏
- [ ] MongoDB 連接穩定
- [ ] 系統回應時間正常

---

## 🔄 **遷移計畫**

### **遷移概述**

#### **目標**
將現有的 MongoDB 日誌系統從不穩定的架構遷移到新的彈性、可靠的日誌系統，解決循環引用、連接失敗和資料序列化問題。

#### **遷移範圍**
- **服務層**: 從 `MongoDBLoggerService` 遷移到 `ResilientMongoDBLoggerService`
- **資料模型**: 從複雜的 `MongoDBLogEntry` 遷移到簡化的 `SimpleLogEntry`
- **連接管理**: 引入專用的 `MongoDBConnectionManager`
- **資料處理**: 引入 `LogDataProcessor` 進行安全序列化
- **錯誤處理**: 實現熔斷器模式和降級機制

### **遷移時程表**

#### **階段 1: 準備階段 (1-2 天)**
- [x] 備份現有 MongoDB 日誌資料
- [x] 建立測試環境
- [x] 驗證新系統組件編譯
- [x] 準備回滾計畫

#### **階段 2: 並行部署 (2-3 天)**
- [x] 部署新服務但保持舊服務運行
- [x] 配置雙寫模式（同時寫入新舊系統）
- [x] 監控新系統穩定性
- [x] 驗證資料一致性

#### **階段 3: 切換階段 (1 天)**
- [x] 停止寫入舊系統
- [x] 完全切換到新系統
- [x] 監控系統運行狀況
- [x] 驗證所有功能正常

#### **階段 4: 清理階段 (已完成)**
- [x] 移除舊系統程式碼
- [x] 清理舊的依賴注入配置
- [x] 更新文檔
- [x] 完成遷移驗證

---

## 📚 **最佳實踐**

### **開發建議**
1. **適當的日誌級別**: 使用正確的日誌級別，避免過度記錄
2. **有意義的訊息**: 提供清晰、有用的日誌訊息
3. **交易識別碼**: 使用一致的交易識別碼關聯相關操作
4. **錯誤處理**: 妥善處理日誌記錄失敗，不影響業務邏輯

### **使用範例**

#### **基本日誌記錄**
```csharp
public class PartnerService
{
    private readonly ILoggerService _logger;

    public PartnerService(ILoggerService logger)
    {
        _logger = logger;
    }

    public async Task<Partner> CreatePartnerAsync(PartnerDto dto)
    {
        try
        {
            // 業務邏輯
            var partner = new Partner { /* ... */ };

            // 記錄操作日誌
            await _logger.LogInfoAsync($"新增夥伴: {partner.Name}", "PartnerService");

            return partner;
        }
        catch (Exception ex)
        {
            // 記錄錯誤日誌
            await _logger.LogErrorAsync("新增夥伴失敗", ex, "PartnerService");
            throw;
        }
    }
}
```

#### **實體變更自動記錄**
```csharp
// ERPDbContext 中已自動整合，無需額外程式碼
public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
{
    // 系統自動捕獲實體變更並記錄日誌
    var result = await base.SaveChangesAsync(cancellationToken);
    return result;
}
```

---

## 📝 **總結**

### **系統優勢**
- ✅ **穩定可靠**: 解決了循環引用和連接失敗問題
- ✅ **效能優異**: 優化的序列化和連接管理
- ✅ **易於維護**: 清晰的架構和完整的監控
- ✅ **擴展性強**: 支援未來功能擴展和效能調優

### **關鍵成果**
- 成功解決了 Entity Framework 循環引用序列化問題
- 實現了穩定的 MongoDB 連接管理
- 提供了完整的審計追蹤功能
- 建立了彈性的錯誤處理機制

### **後續維護**
- 定期監控系統健康狀態
- 根據業務需求調整日誌保留策略
- 持續優化效能和資源使用
- 保持文檔更新和團隊培訓
